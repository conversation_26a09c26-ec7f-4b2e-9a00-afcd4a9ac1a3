{"length": 5, "scenario": "RESTAURANT_RECOMMENDATION", "scenario_id": 1, "tasks": [{"task_id": "1_late_night_korean_food", "instruction": "Find the best-rated late-night Korean restaurant in Champaign, IL that opens beyond 9pm on Google Maps.", "type": "single_app", "apps": ["Maps"], "rubrics": ["Opened Maps", "Searched for Korean restaurants in Champaign, IL", "Checked at least one restaurant's rating", "Checked at least one restaurant's opening hours", "Checked multiple restaurants' ratings and opening hours", "Stopped at the screen showing a highly-rated korean restaurant in Champaign, IL that opens beyond 9pm"], "human_reference_operations": ["open maps", "tap on search bar", "type 'late-night Korean restaurants in Champaign, IL'", "tap enter", "tap filters", "tap 'Custom' under Hours", "tap 'Any time'", "swipe up to see more time", "swipe up again", "swipe up again", "tap 10 pm", "tap 'Apply'", "swipe up to check more ratings", "swipe up to check more ratings and select one with high rating"]}, {"task_id": "1_nearest_bakery", "instruction": "Get directions to the nearest Bakery that has a rating higher than 4.0 on Google Maps. Stop at the screen showing the route.", "type": "single_app", "apps": ["Maps"], "rubrics": ["Opened Maps", "Searched for bakeries near me", "Found a bakery with a rating higher than 4.0", "Found a bakery that is near to me", "Found the bakery that is nearest to me with a rating higher than 4.0", "Stopped at the screen showing the route to the nearest bakery with a rating higher than 4.0"], "human_reference_operations": ["open maps", "tap on search bar", "type 'bakery near me'", "tap enter", "tap filters", "tap 'Distance'", "tap 'Apply'", "swipe up to find a bakery with a rating higher than 4.0", "swipe up to again", "tap 'Directions' on the bakery found"]}, {"task_id": "1_thai_duck", "instruction": "Find the best-rated Thai restaurant in Urbana, IL that serves duck cuisine on Google Maps. Review customer comments and compile a summary of positive and negative feedback in Notes.", "type": "multi_app", "apps": ["Maps", "Notes"], "rubrics": ["Opened Maps", "Searched for Thai restaurants in Urbana, IL", "Checked at least one restaurant's rating", "Confirmed at least one restaurant serves duck dishes", "Checked multiple restaurants' ratings", "Reviewed customer comments of the chosen restaurant", "Opened Notes", "Added a Note with a summary of customer feedback", "The summary includes both positive and negative feedback"], "human_reference_operations": ["open maps", "tap on search bar", "type 'Thai restaurants in Urbana, IL'", "tap enter", "swipe up to see more results", "tap on a Thai restaurant (Siam Terrace)", "tap Menu", "swipe up to view highlight dishes", "tap show more", "swipe up to see more dishes", "swipe up to see more dishes", "swipe up to see more dishes", "swipe left on the sub-menu bar to nagivate to main courses", "swipe left on the sub-menu bar to nagivate to main courses", "tap Menu-Siam Terrace's House", "swipe up to see more dishes (found <PERSON>)", "tap Reviews", "swipe up tp see more reviews", "swipe up tp see more reviews to find negative reviews", "swipe up tp see more reviews to find negative reviews", "swipe up tp see more reviews to find negative reviews", "tap more on a review with 4 stars (not 5 stars)", "go back", "swipe up tp see more reviews to find more nagative comments", "swipe up tp see more reviews to find more nagative comments", "swipe up tp see more reviews to find more nagative comments (found one mentioning cold breeze)", "tap home", "open notes", "tap new note", "type 'Summary of comments for Siam Terrace's...'"]}, {"task_id": "1_bakery_birthday_cake", "instruction": "Find me a Bakery that is within 10min drive near me and does birthday cakes on Google Maps. Find the phone number and create a new note in Notes for that.", "type": "multi_app", "apps": ["Maps", "Notes"], "rubrics": ["Opened Maps", "Found a bakery within 10min drive", "Confirmed they offer birthday cakes", "Retrieved bakery's phone number", "Opened Notes", "Created a new note with the bakery's phone number"], "human_reference_operations": ["open maps", "tap on search bar", "type 'bakery birthday cakes near me'", "tap enter", "tap Directions to check the distance on the first result", "tap Back (see the drive time is 3 min)", "tap the call button to get the phone number", "tap home", "open notes", "tap new note", "type 'the bakery's phone number...'"]}, {"task_id": "1_chinese_ohare", "instruction": "Find me a popular Chinese restaurant near Chicago O'Hare airport on Google Maps. Check X for recent posts about their signature dishes and write a summary in Notes. Then get directions to that restaurant on Google Maps. Stop at the screen showing the route.", "type": "multi_app", "apps": ["Maps", "X", "Notes"], "rubrics": ["Opened Maps", "Searched for Chinese restaurants near Chicago O'Hare airport", "Selected a popular restaurant (high rating/reviews)", "Opened X", "Searched for recent posts about signature dishes of the chosen restaurant", "Opened Notes", "Summarized the signature dishes and feedback", "Returned to Maps", "Got directions to the chosen restaurant, stopped at the route screen"], "human_reference_operations": ["open maps", "tap on search bar", "type 'Chinese Restaurant near Chicago O'Hare'", "tap enter", "tap on the map to find a high-rated one (New Star Restaurant)", "tap Home", "open X", "tap the search button", "type on the search bar", "type 'New Star Restaurant'", "tap enter", "swipe up to see more posts (can not find any posts about new star restaurant)", "switch app", "tap Maps (find reviews on Maps instead)", "tap reviews (checked the AI summarized review containing dishes such as egg rolls)", "tap home", "open notes", "tap new note", "type 'New Star Restaurant dishes...'", "switch app", "tap Maps", "tap Directions button"]}]}