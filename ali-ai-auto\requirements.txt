# 核心依赖
torch>=2.0.0
torchvision>=0.15.0
transformers>=4.30.0
opencv-python>=4.8.0
timm>=0.9.0

# 文本处理 - 使用预编译版本避免编译错误
# sentencepiece>=0.1.99  # 暂时跳过，稍后单独安装

# 计算机视觉
pyclipper>=1.3.0
supervision==0.21.0
shapely>=2.0.0

# 工具库
yapf>=0.32.0
sortedcontainers>=2.4.0
simplejson>=3.19.0
addict>=2.4.0
termcolor>=2.3.0
datasets>=2.14.0
matplotlib>=3.7.0

# ModelScope 和 DashScope
modelscope>=1.9.0
dashscope>=1.14.0

# 可选：如果上面的 pycocotools 安装失败，可以注释掉这行
# pycocotools>=2.0.6
